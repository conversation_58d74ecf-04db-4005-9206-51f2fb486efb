// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {BottomSheetFooter, type BottomSheetFooterProps, SHEET_STATE, useBottomSheet, useBottomSheetInternal} from '@gorhom/bottom-sheet';
import React, {useCallback} from 'react';
import {Platform, View, Text, TouchableOpacity, ScrollView, StyleSheet} from 'react-native';
import Animated, {useAnimatedStyle, withTiming} from 'react-native-reanimated';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {useKeyboardHeight} from '@hooks/device';
import {selectEmojiCategoryBarSection} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import EmojiCategoryBar from '../emoji_category_bar';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

type PickerFooterProps = BottomSheetFooterProps & {
    selectedEmojis?: SelectedEmoji[];
    onRemoveEmoji?: (id: string) => void;
    onDone?: () => void;
    showPreview?: boolean;
};

const getPreviewStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        previewContainer: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderTopWidth: 1,
            borderTopColor: changeOpacity(theme.centerChannelColor, 0.08),
            paddingHorizontal: 16,
            paddingVertical: 12,
            maxHeight: 80,
        },
        previewHeader: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 8,
        },
        previewTitle: {
            fontSize: 14,
            fontWeight: '600',
            color: theme.centerChannelColor,
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 6,
            borderRadius: 16,
        },
        doneButtonText: {
            color: theme.buttonColor,
            fontSize: 14,
            fontWeight: '600',
        },
        scrollContainer: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.6),
            fontSize: 12,
            fontStyle: 'italic',
            textAlign: 'center',
            flex: 1,
        },
        emojiItem: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: changeOpacity(theme.buttonBg, 0.12),
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            marginRight: 6,
            borderWidth: 1,
            borderColor: changeOpacity(theme.buttonBg, 0.25),
        },
        emojiCharacter: {
            fontSize: 16,
            marginRight: 4,
        },
        removeButton: {
            padding: 2,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.1),
        },
        removeIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.7),
        },
    });
});

const PickerFooter = (props: PickerFooterProps) => {
    const {selectedEmojis = [], onRemoveEmoji, onDone, showPreview = false, ...bottomSheetProps} = props;
    const theme = useTheme();
    const keyboardHeight = useKeyboardHeight();
    const {animatedSheetState} = useBottomSheetInternal();
    const {expand} = useBottomSheet();
    const previewStyles = getPreviewStyleSheet(theme);

    const scrollToIndex = useCallback((index: number) => {
        if (animatedSheetState.value === SHEET_STATE.EXTENDED) {
            selectEmojiCategoryBarSection(index);
            return;
        }
        expand();

        // @ts-expect-error wait until the bottom sheet is epanded
        while (animatedSheetState.value !== SHEET_STATE.EXTENDED) {
            // do nothing
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    const handleRemoveEmoji = useCallback((id: string) => {
        onRemoveEmoji?.(id);
    }, [onRemoveEmoji]);

    const handleDone = useCallback(() => {
        onDone?.();
    }, [onDone]);

    const animatedStyle = useAnimatedStyle(() => {
        const paddingBottom = withTiming(
            Platform.OS === 'ios' ? 20 : 0,
            {duration: 250},
        );
        return {backgroundColor: theme.centerChannelBg, paddingBottom};
    }, [theme]);

    const heightAnimatedStyle = useAnimatedStyle(() => {
        let baseHeight = 55;
        let previewHeight = showPreview && selectedEmojis.length > 0 ? 80 : 0;
        let height = baseHeight + previewHeight;

        if (keyboardHeight === 0 && Platform.OS === 'ios') {
            height += 20;
        } else if (keyboardHeight) {
            height = 0;
        }

        return {
            height,
        };
    }, [keyboardHeight, showPreview, selectedEmojis.length]);

    const renderEmojiPreview = () => {
        if (!showPreview) {
            return null;
        }

        return (
            <View style={previewStyles.previewContainer}>
                <View style={previewStyles.previewHeader}>
                    <Text style={previewStyles.previewTitle}>
                        Selected ({selectedEmojis.length})
                    </Text>
                    {selectedEmojis.length > 0 && (
                        <TouchableOpacity
                            style={previewStyles.doneButton}
                            onPress={handleDone}
                        >
                            <Text style={previewStyles.doneButtonText}>
                                Done
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
                {selectedEmojis.length === 0 ? (
                    <Text style={previewStyles.emptyText}>
                        Tap emojis to select them
                    </Text>
                ) : (
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={previewStyles.scrollContainer}
                    >
                        {selectedEmojis.map((emoji) => (
                            <View
                                key={emoji.id}
                                style={previewStyles.emojiItem}
                            >
                                <Text style={previewStyles.emojiCharacter}>
                                    {emoji.character}
                                </Text>
                                <TouchableOpacity
                                    style={previewStyles.removeButton}
                                    onPress={() => handleRemoveEmoji(emoji.id)}
                                    hitSlop={{top: 4, bottom: 4, left: 4, right: 4}}
                                >
                                    <CompassIcon
                                        name='close'
                                        size={10}
                                        style={previewStyles.removeIcon}
                                    />
                                </TouchableOpacity>
                            </View>
                        ))}
                    </ScrollView>
                )}
            </View>
        );
    };

    return (
        <BottomSheetFooter
            style={heightAnimatedStyle}
            {...bottomSheetProps}
        >
            <Animated.View style={[animatedStyle]}>
                {renderEmojiPreview()}
                <EmojiCategoryBar onSelect={scrollToIndex}/>
            </Animated.View>
        </BottomSheetFooter>
    );
};

export default PickerFooter;
